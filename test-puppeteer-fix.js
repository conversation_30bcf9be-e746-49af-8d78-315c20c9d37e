/**
 * Test script to verify the Puppeteer JSON parsing fix
 * This script tests both traditional HTTP and Puppeteer methods
 */

import { API } from './src/index.js';

async function testTraditionalHTTP() {
	console.log('Testing traditional HTTP method...');
	
	const api = new API({
		usePuppeteer: false,
	});

	try {
		const search = await api.search('test', 1);
		console.log(`✅ Traditional HTTP: Found ${search.books.length} books`);
		return true;
	} catch (error) {
		console.error(`❌ Traditional HTTP failed: ${error.message}`);
		return false;
	}
}

async function testPuppeteerMethod() {
	console.log('Testing Puppeteer method...');
	
	const api = new API({
		usePuppeteer: true,
		browserArgs: [
			'--no-sandbox',
			'--disable-setuid-sandbox',
			'--disable-dev-shm-usage',
			'--disable-gpu',
		],
	});

	try {
		const search = await api.search('test', 1);
		console.log(`✅ Puppeteer: Found ${search.books.length} books`);
		return true;
	} catch (error) {
		console.error(`❌ Puppeteer failed: ${error.message}`);
		
		if (error.message.includes('Puppeteer dependencies not found')) {
			console.log('ℹ️  To test Puppeteer, install: npm install puppeteer-extra puppeteer-extra-plugin-stealth');
			return 'skipped';
		}
		return false;
	}
}

async function main() {
	console.log('=== Testing Puppeteer JSON Parsing Fix ===\n');
	
	const traditionalResult = await testTraditionalHTTP();
	console.log('');
	
	const puppeteerResult = await testPuppeteerMethod();
	console.log('');
	
	console.log('=== Results ===');
	console.log(`Traditional HTTP: ${traditionalResult ? '✅ PASS' : '❌ FAIL'}`);
	console.log(`Puppeteer: ${puppeteerResult === 'skipped' ? '⏭️  SKIPPED' : puppeteerResult ? '✅ PASS' : '❌ FAIL'}`);
	
	if (traditionalResult && (puppeteerResult === true || puppeteerResult === 'skipped')) {
		console.log('\n🎉 All tests completed successfully!');
	} else {
		console.log('\n⚠️  Some tests failed. Check the error messages above.');
	}
}

main().catch(console.error);
