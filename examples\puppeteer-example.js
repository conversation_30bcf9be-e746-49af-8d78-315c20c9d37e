/**
 * Example demonstrating how to use the nhentai-api with Puppeteer support
 * 
 * Prerequisites:
 * npm install puppeteer-extra puppeteer-extra-plugin-stealth
 */

import { API } from '../src/index.js';

async function exampleWithPuppeteer() {
	console.log('Creating API instance with Puppeteer support...');
	
	// Create API instance with Puppeteer enabled
	const api = new API({
		usePuppeteer: true,
		browserArgs: [
			'--no-sandbox',
			'--disable-setuid-sandbox',
			'--disable-dev-shm-usage', // Overcome limited resource problems
			'--disable-accelerated-2d-canvas',
			'--no-first-run',
			'--no-zygote',
			'--disable-gpu',
		],
		cookies: 'sessionid=example;csrftoken=example', // Optional cookies
	});

	try {
		console.log('Searching for books...');
		
		// This will use Puppeteer with stealth plugin instead of native HTTP requests
		// The implementation automatically handles JSON parsing from API responses
		const search = await api.search('test', 1);
		
		console.log(`Found ${search.books.length} books on page ${search.page} of ${search.pages}`);
		
		if (search.books.length > 0) {
			const firstBook = search.books[0];
			console.log(`First book: ${firstBook.title.pretty} (ID: ${firstBook.id})`);
			
			// Test getting a specific book
			console.log('Getting book details...');
			const bookDetails = await api.getBook(firstBook.id);
			console.log(`Book details loaded: ${bookDetails.pages.length} pages`);
		}
		
	} catch (error) {
		console.error('Error:', error.message);
		
		// If Puppeteer dependencies are not installed, you'll get a helpful error message
		if (error.message.includes('Puppeteer dependencies not found')) {
			console.log('\nTo use Puppeteer support, install the required dependencies:');
			console.log('npm install puppeteer-extra puppeteer-extra-plugin-stealth');
		} else if (error.message.includes('Invalid JSON response')) {
			console.log('\nThis might be a parsing issue. The API response format may have changed.');
			console.log('Try using traditional HTTP requests instead.');
		}
	}
}

async function exampleWithoutPuppeteer() {
	console.log('\nCreating API instance without Puppeteer (traditional HTTP requests)...');
	
	// Create API instance without Puppeteer (default behavior)
	const api = new API({
		usePuppeteer: false, // This is the default
	});

	try {
		console.log('Searching for books...');
		
		// This will use native HTTP requests
		const search = await api.search('test', 1);
		
		console.log(`Found ${search.books.length} books on page ${search.page} of ${search.pages}`);
		
	} catch (error) {
		console.error('Error:', error.message);
	}
}

// Run examples
async function main() {
	console.log('=== nhentai-api Puppeteer Example ===\n');
	
	// Example with Puppeteer
	await exampleWithPuppeteer();
	
	// Example without Puppeteer
	await exampleWithoutPuppeteer();
	
	console.log('\n=== Example completed ===');
}

main().catch(console.error);
