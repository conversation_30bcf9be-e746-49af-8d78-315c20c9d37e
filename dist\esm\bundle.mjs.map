{"version": 3, "file": "bundle.mjs", "sources": ["../../src/tag.js", "../../src/image.js", "../../src/tagsArray.js", "../../src/book.js", "../../src/search.js", "../../src/error.js", "../../src/api.js", "../../src/options.js", "../../src/index.js"], "sourcesContent": ["/**\r\n * @module Tag\r\n */\r\n\r\n/**\r\n * Tag object from API.\r\n * @global\r\n * @typedef {object} APITag\r\n * @property {number|string} id    Tag id.\r\n * @property {string}        type  Tag type.\r\n * @property {string}        name  Tag name.\r\n * @property {number|string} count Tagged books count.\r\n * @property {string}        url   Tag URL.\r\n */\r\n\r\n/**\r\n * @typedef {object} TagTypes\r\n * @property {UnknownTagType} Unknown   Unknown tag type.\r\n * @property {TagType}        Tag       Tag tag type.\r\n * @property {TagType}        Category  Category tag type.\r\n * @property {TagType}        Artist    Artist tag type.\r\n * @property {TagType}        Parody    Parody tag type.\r\n * @property {TagType}        Character Character tag type.\r\n * @property {TagType}        Group     Group tag type.\r\n * @property {TagType}        Language  Language tag type.\r\n */\r\n\r\n/**\r\n * Class representing tag type.\r\n * @class\r\n */\r\nclass TagType {\r\n\t/**\r\n\t * @type {TagTypes}\r\n\t * @static\r\n\t * @default {}\r\n\t */\r\n\tstatic knownTypes = {};\r\n\r\n\t/**\r\n\t * Tag type name.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\ttype = null;\r\n\r\n\t/**\r\n\t * Create tag type.\r\n\t * @param {string} type Tag type.\r\n\t */\r\n\tconstructor(type) {\r\n\t\tif (type) {\r\n\t\t\tthis.type = type;\r\n\t\t\tthis.constructor.knownTypes[type] = this;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Check if this tag type is unknown.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isKnown() {\r\n\t\treturn !(this instanceof UnknownTagType);\r\n\t}\r\n\r\n\t/**\r\n\t * Tag type name.\r\n\t * @returns {string}\r\n\t */\r\n\ttoString() {\r\n\t\treturn this.type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing unknown tag type.\r\n * @class\r\n * @extends TagType\r\n */\r\nclass UnknownTagType extends TagType {\r\n\t/**\r\n\t * Create unknown tag type.\r\n\t * @param {string} [type=\"unknown\"] Unknown tag type name.\r\n\t */\r\n\tconstructor(type = 'unknown') {\r\n\t\tsuper(null);\r\n\t\tthis.type = type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing tag.\r\n * @class\r\n */\r\nclass Tag {\r\n\t/**\r\n\t * Tag types.\r\n\t * @type {TagTypes}\r\n\t * @static\r\n\t */\r\n\tstatic types = {\r\n\t\tUnknown  : new UnknownTagType(), // Symbol('unknown')\r\n\t\tTag      : new TagType('tag'),\r\n\t\tCategory : new TagType('category'),\r\n\t\tArtist   : new TagType('artist'),\r\n\t\tParody   : new TagType('parody'),\r\n\t\tCharacter: new TagType('character'),\r\n\t\tGroup    : new TagType('group'),\r\n\t\tLanguage : new TagType('language'),\r\n\r\n\t\t/**\r\n\t\t * Known tag types.\r\n\t\t * @type {TagTypes}\r\n\t\t */\r\n\t\tknown: TagType.knownTypes,\r\n\r\n\t\t/**\r\n\t\t * Get tag type class instance by name.\r\n\t\t * @param {string} type Tag type.\r\n\t\t * @returns {TagType|UnknownTagType} Tag type class instance.\r\n\t\t */\r\n\t\tget(type) {\r\n\t\t\tlet known;\r\n\t\t\tif ('string' === typeof type)\r\n\t\t\t\ttype = type.toLowerCase();\r\n\t\t\treturn ((known = this.known[type]))\r\n\t\t\t\t? known\r\n\t\t\t\t: new UnknownTagType(type);\r\n\t\t},\r\n\t};\r\n\r\n\t/**\r\n\t * Warp tag object with Tag class instance.\r\n\t * @param {APITag|Tag} tag Tag to wrap.\r\n\t * @returns {Tag} Tag.\r\n\t * @static\r\n\t */\r\n\tstatic get(tag) {\r\n\t\tif (!(tag instanceof this))\r\n\t\t\ttag = new this({\r\n\t\t\t\tid   : +tag.id,\r\n\t\t\t\ttype : tag.type,\r\n\t\t\t\tname : tag.name,\r\n\t\t\t\tcount: +tag.count,\r\n\t\t\t\turl  : tag.url,\r\n\t\t\t});\r\n\t\treturn tag;\r\n\t}\r\n\r\n\t/**\r\n\t * Tag ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tid = 0;\r\n\r\n\t/**\r\n\t * Tag type.\r\n\t * @type {TagType|UnknownTagType}\r\n\t * @default TagTypes.Unknown\r\n\t */\r\n\ttype = this.constructor.types.Unknown;\r\n\r\n\t/**\r\n\t * Tag name.\r\n\t * @type {string}\r\n\t * @default \"\"\r\n\t */\r\n\tname = '';\r\n\r\n\t/**\r\n\t * Count of books tagged with this tag.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tcount = 0;\r\n\r\n\t/**\r\n\t * Tag URL.\r\n\t * @type {string}\r\n\t * @default \"\"\r\n\t */\r\n\turl = '';\r\n\r\n\t/**\r\n\t * Create tag.\r\n\t * @param {object}         [params]                       Tag parameters.\r\n\t * @param {number}         [params.id=0]                  Tag id.\r\n\t * @param {string|TagType} [params.type=TagTypes.Unknown] Tag type.\r\n\t * @param {string}         [params.name=\"\"]               Tag name.\r\n\t * @param {number}         [params.count=0]               Tagged books count.\r\n\t * @param {string}         [params.url=\"\"]                Tag URL.\r\n\t */\r\n\tconstructor({\r\n\t\tid    = 0,\r\n\t\ttype  = this.constructor.types.Unknown,\r\n\t\tname  = '',\r\n\t\tcount = 0,\r\n\t\turl   = '',\r\n\t} = {}) {\r\n\t\tObject.assign(this, {\r\n\t\t\tid,\r\n\t\t\ttype: type instanceof TagType\r\n\t\t\t\t? type\r\n\t\t\t\t: this.constructor.types.get(type),\r\n\t\t\tname,\r\n\t\t\tcount,\r\n\t\t\turl,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Compare this to given one.\r\n\t * By default tags with different id will return false.\r\n\t * If you want to check whatever tag has any of properties from another tag pass `'any'` to `strict` parameter.\r\n\t * @param {string|Tag} tag                Tag to compare with.\r\n\t * @param {boolean|string} [strict=false] Whatever all parameters must be the same.\r\n\t * @returns {boolean} Whatever tags are equal.\r\n\t */\r\n\tcompare(tag, strict = false) {\r\n\t\ttag = this.constructor.get(tag);\r\n\t\tif (strict === 'any')\r\n\t\t\tstrict = false;\r\n\t\telse if (this.id !== tag.id)\r\n\t\t\treturn false;\r\n\r\n\t\treturn !![\r\n\t\t\t'id',\r\n\t\t\t'type',\r\n\t\t\t'name',\r\n\t\t\t'count',\r\n\t\t\t'url',\r\n\t\t].map(\r\n\t\t\tprop => tag[prop] === this[prop]\r\n\t\t).reduce(\r\n\t\t\t(accum, current) => strict\r\n\t\t\t\t? accum * current\r\n\t\t\t\t: accum + current\r\n\t\t);\r\n\t}\r\n\r\n\t/**\r\n\t * Get tag name or tag name with count of tagged books.\r\n\t * @param {?boolean} [includeCount=false] Include count.\r\n\t * @returns {string}\r\n\t */\r\n\ttoString(includeCount = false) {\r\n\t\treturn this.name + (includeCount\r\n\t\t\t? ` (${this.count})`\r\n\t\t\t: '');\r\n\t}\r\n}\r\n\r\nexport {\r\n\tTag,\r\n\tTagType,\r\n\tUnknownTagType,\r\n};\r\n", "/**\r\n * @module Image\r\n */\r\n\r\nimport Book from './book';\r\n\r\n/**\r\n * Image object from API.\r\n * @global\r\n * @typedef {object} APIImage\r\n * @property {string}        t Image type.\r\n * @property {number|string} w Image width.\r\n * @property {number|string} h Image height.\r\n */\r\n\r\n/**\r\n * @typedef {object} ImageTypes\r\n * @property {TagType} JPEG JPEG image type.\r\n * @property {TagType} PNG  PNG image type.\r\n * @property {TagType} GIF  GIF image type.\r\n */\r\n\r\n/**\r\n * Class representing image type.\r\n * @class\r\n */\r\nclass ImageType {\r\n\t/**\r\n\t * @type {ImageTypes}\r\n\t * @static\r\n\t * @default {}\r\n\t */\r\n\tstatic knownTypes = {};\r\n\r\n\t/**\r\n\t * Image type name.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\ttype = null;\r\n\r\n\t/**\r\n\t * Image type extension.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\textension = null;\r\n\r\n\t/**\r\n\t * Create image type.\r\n\t * @param {string} type      Image type name.\r\n\t * @param {string} extension Image type extension.\r\n\t */\r\n\tconstructor(type, extension) {\r\n\t\tif (type) {\r\n\t\t\tthis.type = type;\r\n\t\t\tthis.constructor.knownTypes[type] = this;\r\n\t\t}\r\n\t\tthis.extension = extension;\r\n\t}\r\n\r\n\t/**\r\n\t * Whatever this tag type is unknown.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isKnown() {\r\n\t\treturn !(this instanceof UnknownImageType);\r\n\t}\r\n\r\n\t/**\r\n\t * Alias for type.\r\n\t * @type {?string}\r\n\t */\r\n\tget name() {\r\n\t\treturn this.type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing unknown image type.\r\n * @class\r\n * @extends ImageType\r\n */\r\nclass UnknownImageType extends ImageType {\r\n\t/**\r\n\t * Create unknown image type.\r\n\t * @param {string} type      Unknown image type name.\r\n\t * @param {string} extension Unknown image type extension.\r\n\t */\r\n\tconstructor(type, extension) {\r\n\t\tsuper(null, extension);\r\n\t\tthis.type = type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing image.\r\n * @class\r\n */\r\nclass Image {\r\n\t/**\r\n\t * Image types.\r\n\t * @type {ImageTypes}\r\n\t * @static\r\n\t */\r\n\tstatic types = {\r\n\t\tJPEG: new ImageType('jpeg', 'jpg'),\r\n\t\tPNG : new ImageType('png', 'png'),\r\n\t\tGIF : new ImageType('gif', 'gif'),\r\n\t\tWEBP: new ImageType('webp', 'webp'),\r\n\r\n\t\tUnknown: new UnknownImageType('unknown', 'unknownExt'),\r\n\r\n\t\t/**\r\n\t\t * Known image types.\r\n\t\t * @type {ImageType}\r\n\t\t */\r\n\t\tknown: ImageType.knownTypes,\r\n\r\n\t\t/**\r\n\t\t * Get image type class instance by name.\r\n\t\t * @param {string} type Image type.\r\n\t\t * @returns {ImageType|UnknownImageType} Image type class instance.\r\n\t\t */\r\n\t\tget(type) {\r\n\t\t\tlet known;\r\n\t\t\tif ('string' === typeof type) {\r\n\t\t\t\ttype = type.toLowerCase();\r\n\t\t\t\tswitch (type) {\r\n\t\t\t\t\tcase 'j':\r\n\t\t\t\t\tcase 'jpg':\r\n\t\t\t\t\tcase 'jpeg':\r\n\t\t\t\t\t\ttype = 'jpeg';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'p':\r\n\t\t\t\t\tcase 'png':\r\n\t\t\t\t\t\ttype = 'png';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'g':\r\n\t\t\t\t\tcase 'gif':\r\n\t\t\t\t\t\ttype = 'gif';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'w':\r\n\t\t\t\t\tcase 'webp':\r\n\t\t\t\t\t\ttype = 'webp';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn ((known = this.known[type]))\r\n\t\t\t\t? known\r\n\t\t\t\t: new UnknownImageType(type);\r\n\t\t},\r\n\t};\r\n\r\n\t/**\r\n\t * Parse pure image object from API into class instance.\r\n\t * @param {APIImage} image  Image object\r\n\t * @param {number}   [id=0] Image id (a.k.a. page number).\r\n\t * @returns {Image} Image instance.\r\n\t * @static\r\n\t */\r\n\tstatic parse(image, id = 0) {\r\n\t\tlet {\r\n\t\t\tt: type,\r\n\t\t\tw: width,\r\n\t\t\th: height,\r\n\t\t} = image;\r\n\r\n\t\treturn new this({\r\n\t\t\ttype,\r\n\t\t\twidth : +width,\r\n\t\t\theight: +height,\r\n\t\t\tid,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Image ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tid = 0;\r\n\r\n\t/**\r\n\t * Image width.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\twidth = 0;\r\n\r\n\t/**\r\n\t * Image height.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\theight = 0;\r\n\r\n\t/**\r\n\t * Image type.\r\n\t * @type {ImageType}\r\n\t * @default ImageTypes.JPEG\r\n\t */\r\n\ttype = this.constructor.types.JPEG;\r\n\r\n\t/**\r\n\t * Image parent book.\r\n\t * @type {Book}\r\n\t * @default Book.Unknown\r\n\t */\r\n\tbook = Book.Unknown;\r\n\r\n\t/**\r\n\t * Create image.\r\n\t * @param {object}           [params]                      Image parameters.\r\n\t * @param {number}           [params.id=0]                 Image ID.\r\n\t * @param {number}           [params.width=0]              Image width.\r\n\t * @param {number}           [params.height=0]             Image height.\r\n\t * @param {string|ImageType} [params.type=ImageTypes.JPEG] Image type.\r\n\t * @param {Book}             [params.book=Book.Unknown]    Image's Book.\r\n\t */\r\n\tconstructor({\r\n\t\tid     = 0,\r\n\t\twidth  = 0,\r\n\t\theight = 0,\r\n\t\ttype   = this.constructor.types.JPEG,\r\n\t\tbook   = Book.Unknown,\r\n\t} = {}) {\r\n\t\tObject.assign(this, {\r\n\t\t\tid: 'number' === typeof id\r\n\t\t\t\t? id < 1 ? 0 : id\r\n\t\t\t\t: 0,\r\n\t\t\twidth,\r\n\t\t\theight,\r\n\t\t\ttype: type instanceof ImageType\r\n\t\t\t\t? type\r\n\t\t\t\t: this.constructor.types.get(type),\r\n\t\t\tbook: book instanceof Book\r\n\t\t\t\t? book\r\n\t\t\t\t: Book.Unknown,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Whatever this image is book cover.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isCover() {\r\n\t\treturn this.id < 1;\r\n\t}\r\n\r\n\t/**\r\n\t * Image filename.\r\n\t * @type {string}\r\n\t */\r\n\tget filename() {\r\n\t\treturn `${this.isCover ? 'cover' : this.id}.${this.type.extension}`;\r\n\t}\r\n}\r\n\r\nexport default Image;\r\n", "// eslint-disable-next-line no-unused-vars\r\nimport { Tag, } from './tag';\r\n\r\n\r\n/**\r\n * Array of Tags with helper methods.\r\n * @class\r\n * @extends Array<Tag>\r\n */\r\nclass TagsArray extends Array {\r\n\tconstructor(...args) {\r\n\t\tsuper(...args);\r\n\t}\r\n\r\n\t/**\r\n\t * Get array of tags names.\r\n\t * @param {?boolean} [includeCount=false] Include count.\r\n\t * @returns {String[]}\r\n\t */\r\n\ttoNames(includeCount = false) {\r\n\t\treturn Array.from(this, tag => tag.toString(includeCount));\r\n\t}\r\n}\r\n\r\nexport default TagsArray;\r\n", "/**\r\n * @module Book\r\n */\r\n\r\nimport Image from './image';\r\nimport { Tag, } from './tag';\r\nimport TagsArray from './tagsArray';\r\n\r\nimport { TagTypes, } from '.';\r\n\r\n\r\n/**\r\n * Book object from API.\r\n * @global\r\n * @typedef {object} APIBook\r\n * @property {object}        title          Book title.\r\n * @property {string}        title.english  Book english title.\r\n * @property {string}        title.japanese Book japanese title.\r\n * @property {string}        title.pretty   Book short title.\r\n * @property {number|string} id             Book ID.\r\n * @property {number|string} media_id       Book Media ID.\r\n * @property {number|string} num_favorites  Book favours count.\r\n * @property {number|string} num_pages      Book pages count.\r\n * @property {string}        scanlator      Book scanlator.\r\n * @property {number|string} uploaded       Upload UNIX timestamp.\r\n * @property {APIImage}      cover          Book cover image.\r\n * @property {APIImage[]}    images         Book pages' images.\r\n * @property {APITag[]}      tags           Book tags.\r\n */\r\n\r\n/**\r\n * Book title.\r\n * @typedef {object} BookTitle\r\n * @property {string} english  Book english title.\r\n * @property {string} japanese Book japanese title.\r\n * @property {string} pretty   Book short title.\r\n */\r\n\r\n/**\r\n * Class representing Book.\r\n * @class\r\n */\r\nclass Book {\r\n\t/**\r\n\t * Unknown book instance.\r\n\t * @type {UnknownBook}\r\n\t * @static\r\n\t */\r\n\tstatic Unknown;\r\n\r\n\t/**\r\n\t * UnknownBook class.\r\n\t * @type {UnknownBook}\r\n\t * @static\r\n\t */\r\n\tstatic UnknownBook;\r\n\r\n\t/**\r\n\t * Parse book object into class instance.\r\n\t * @param {APIBook} book Book.\r\n\t * @returns {Book} Book instance.\r\n\t * @static\r\n\t */\r\n\tstatic parse(book) {\r\n\t\treturn new this({\r\n\t\t\ttitle    : book.title,\r\n\t\t\tid       : +book.id,\r\n\t\t\tmedia    : +book.media_id,\r\n\t\t\tfavorites: +book.num_favorites,\r\n\t\t\tscanlator: book.scanlator,\r\n\t\t\tuploaded : new Date(+book.upload_date * 1000),\r\n\t\t\ttags     : TagsArray.from(book.tags, tag => Tag.get(tag)),\r\n\t\t\tcover    : Image.parse(book.images.cover),\r\n\t\t\tpages    : book.images.pages.map(\r\n\t\t\t\t(image, id) => Image.parse(image, ++id)\r\n\t\t\t),\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Book title.\r\n\t * @type {BookTitle}\r\n\t */\r\n\ttitle = {\r\n\t\tenglish : '',\r\n\t\tjapanese: '',\r\n\t\tpretty  : '',\r\n\t};\r\n\r\n\t/**\r\n\t * Book ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tid = 0;\r\n\r\n\t/**\r\n\t * Book Media ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tmedia = 0;\r\n\r\n\t/**\r\n\t * Book favours count.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tfavorites = 0;\r\n\r\n\t/**\r\n\t * Book scanlator.\r\n\t * @type {string}\r\n\t * @default ''\r\n\t */\r\n\tscanlator = '';\r\n\r\n\t/**\r\n\t * Book upload date.\r\n\t * @type {Date}\r\n\t * @default new Date(0)\r\n\t */\r\n\tuploaded = new Date(0);\r\n\r\n\t/**\r\n\t * Book tags.\r\n\t * @type {TagsArray}\r\n\t * @default []\r\n\t */\r\n\ttags = new TagsArray();\r\n\r\n\t/**\r\n\t * Book cover.\r\n\t * @type {Image}\r\n\t */\r\n\tcover = new Image({ id: 0, book: this, });\r\n\r\n\t/**\r\n\t * Book pages.\r\n\t * @type {Image[]}\r\n\t * @default []\r\n\t */\r\n\tpages = [];\r\n\r\n\t/**\r\n\t * Create book.\r\n\t * @param {object}          [params]              Book parameters.\r\n\t * @param {BookTitle}       [params.title]        Book title.\r\n\t * @param {number}          [params.id=0]         Book ID.\r\n\t * @param {number}          [params.media=0]      Book Media ID.\r\n\t * @param {number}          [params.favorites=0]  Book favours count.\r\n\t * @param {string}          [params.scanlator=''] Book scanlator.\r\n\t * @param {Date}            [params.uploaded]     Book upload date.\r\n\t * @param {Tag[]|TagsArray} [params.tags=[]]      Book tags.\r\n\t * @param {Image}           [params.cover]        Book cover.\r\n\t * @param {Image[]}         [params.pages=[]]     Book pages.\r\n\t */\r\n\tconstructor({\r\n\t\ttitle     = {\r\n\t\t\tenglish : '',\r\n\t\t\tjapanese: '',\r\n\t\t\tpretty  : '',\r\n\t\t},\r\n\t\tid        = 0,\r\n\t\tmedia     = 0,\r\n\t\tfavorites = 0,\r\n\t\tscanlator = '',\r\n\t\tuploaded  = new Date(0),\r\n\t\ttags      = new TagsArray(),\r\n\t\tcover     = new Image({ id: 0, book: this, }),\r\n\t\tpages     = [],\r\n\t} = {}) {\r\n\t\tthis.setCover(cover);\r\n\r\n\t\tif (Array.isArray(pages))\r\n\t\t\tpages.forEach(this.pushPage.bind(this));\r\n\r\n\t\tif (Array.isArray(tags))\r\n\t\t\ttags.forEach(this.pushTag.bind(this));\r\n\r\n\t\tObject.assign(this, {\r\n\t\t\ttitle,\r\n\t\t\tid,\r\n\t\t\tmedia,\r\n\t\t\tfavorites,\r\n\t\t\tscanlator,\r\n\t\t\tuploaded,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Check whatever book is known.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isKnown() {\r\n\t\treturn !(this instanceof UnknownBook);\r\n\t}\r\n\r\n\t/**\r\n\t * Set book cover image.\r\n\t * @param {Image} cover Image.\r\n\t * @returns {boolean} Whatever cover was set.\r\n\t * @private\r\n\t */\r\n\tsetCover(cover) {\r\n\t\tif (cover instanceof Image) {\r\n\t\t\tcover.book = this;\r\n\t\t\tthis.cover = cover;\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Push image to book pages.\r\n\t * @param {Image} page Image.\r\n\t * @returns {boolean} Whatever page was added.\r\n\t * @private\r\n\t */\r\n\tpushPage(page) {\r\n\t\tif (page instanceof Image) {\r\n\t\t\tpage.book = this;\r\n\t\t\tthis.pages.push(page);\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Push tag to book tags.\r\n\t * @param {Tag} tag Tag.\r\n\t * @returns {boolean} Whatever tag was added.\r\n\t * @private\r\n\t */\r\n\tpushTag(tag) {\r\n\t\ttag = Tag.get(tag);\r\n\r\n\t\tif (!this.hasTag(tag)) {\r\n\t\t\tthis.tags.push(tag);\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Check if book has certain tag.\r\n\t * @param {Tag}     tag            Tag\r\n\t * @param {boolean} [strict=false] Strict comparison.\r\n\t */\r\n\thasTag(tag, strict = true) {\r\n\t\ttag = Tag.get(tag);\r\n\r\n\t\treturn this.tags.some(elem => elem.compare(tag, strict));\r\n\t}\r\n\r\n\t/**\r\n\t * Check if book has any tags with certain properties.\r\n\t * @param {object|Tag} tag Tag.\r\n\t */\r\n\thasTagWith(tag) {\r\n\t\treturn this.hasTag(tag, 'any');\r\n\t}\r\n\r\n\t/**\r\n\t * Get any tags with certain properties.\r\n\t * @param {object|Tag} tag Tag.\r\n\t * @returns {TagsArray}\r\n\t */\r\n\tgetTagsWith(tag) {\r\n\t\ttag = Tag.get(tag);\r\n\r\n\t\treturn this.tags.filter(elem => elem.compare(tag, 'any'));\r\n\t}\r\n\r\n\t/**\r\n\t * Pure tags (with type {TagType.Tag}).\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget pureTags() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Tag, });\r\n\t}\r\n\r\n\t/**\r\n\t * Category tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget categories() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Category, });\r\n\t}\r\n\r\n\t/**\r\n\t * Artist tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget artists() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Artist, });\r\n\t}\r\n\r\n\t/**\r\n\t * Parody tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget parodies() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Parody, });\r\n\t}\r\n\r\n\t/**\r\n\t * Character tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget characters() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Character, });\r\n\t}\r\n\r\n\t/**\r\n\t * Group tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget groups() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Group, });\r\n\t}\r\n\r\n\t/**\r\n\t * Language tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget languages() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Language, });\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing unknown book.\r\n * @class\r\n * @extends Book\r\n */\r\nclass UnknownBook extends Book {\r\n\t/**\r\n\t * Create unknown book.\r\n\t */\r\n\tconstructor() {\r\n\t\tsuper({});\r\n\t}\r\n}\r\n\r\nBook.UnknownBook = UnknownBook;\r\nBook.Unknown = new UnknownBook();\r\n\r\nexport default Book;\r\n", "/**\r\n * @module Search\r\n */\r\n\r\nimport API from './api';\r\nimport Book from './book';\r\nimport { Tag, } from './tag';\r\n\r\n\r\n/**\r\n * Search object from API.\r\n * @global\r\n * @typedef {object} APISearch\r\n * @property {APIBook[]}     result    Search results.\r\n * @property {number|string} num_pages Number of search pages available.\r\n * @property {number|string} per_page  Number of books per page.\r\n */\r\n\r\n\r\n/**\r\n * @typedef {''|'popular'|'popular-week'|'popular-today'|'popular-month'} SearchSortMode\r\n */\r\n\r\nclass SearchSort {\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic Recent = '';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic Popular = 'popular';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic PopularMonth = 'popular-month';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic PopularWeek = 'popular-week';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic PopularToday = 'poplar-today';\r\n}\r\n\r\n/**\r\n * Class representing search request results.\r\n * @class\r\n */\r\nclass Search {\r\n\t/**\r\n\t * Parse search object into class instance.\r\n\t * @param {APISearch} search Search object.\r\n\t */\r\n\tstatic parse(search) {\r\n\t\treturn new this({\r\n\t\t\tpages: search.num_pages\r\n\t\t\t\t? +search.num_pages\r\n\t\t\t\t: 1,\r\n\t\t\tperPage: search.per_page\r\n\t\t\t\t? +search.per_page\r\n\t\t\t\t: search.result.length,\r\n\t\t\tbooks: search.result.map(Book.parse.bind(Book)),\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * API instance.\r\n\t * @type {?API}\r\n\t * @default null\r\n\t */\r\n\tapi = null;\r\n\r\n\t/**\r\n\t * Query string.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\tquery = null;\r\n\r\n\t/**\r\n\t * Search sort mode.\r\n\t * @type {SearchSortMode}\r\n\t * @default ''\r\n\t */\r\n\tsort = '';\r\n\r\n\t/**\r\n\t * Page ID.\r\n\t * @type {number}\r\n\t * @default 1\r\n\t */\r\n\tpage = 1;\r\n\r\n\t/**\r\n\t * Books per page.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tperPage = 0;\r\n\r\n\t/**\r\n\t * Books array.\r\n\t * @type {Book[]}\r\n\t * @default []\r\n\t */\r\n\tbooks = [];\r\n\r\n\t/**\r\n\t * Pages count.\r\n\t * @type {number}\r\n\t * @default 1\r\n\t */\r\n\tpages = 1;\r\n\r\n\t/**\r\n\t * Create search.\r\n\t * @param {?object}         [params]           Search parameters.\r\n\t * @param {?string}         [params.query='']  Query string.\r\n\t * @param {?SearchSortMode} [params.sort='']   Search sort mode.\r\n\t * @param {?number}         [params.page=1]    Search page ID.\r\n\t * @param {?number}         [params.pages=1]   Search pages count.\r\n\t * @param {?number}         [params.perPage=0] Search books per page.\r\n\t * @param {?Book[]}         [params.books=[]]  Books array.\r\n\t */\r\n\tconstructor({\r\n\t\tquery   = null,\r\n\t\tsort    = '',\r\n\t\tpage    = 1,\r\n\t\tpages   = 1,\r\n\t\tperPage = 0,\r\n\t\tbooks   = [],\r\n\t} = {}) {\r\n\t\tif (Array.isArray(books))\r\n\t\t\tbooks.forEach(this.pushBook.bind(this));\r\n\r\n\t\tObject.assign(this, {\r\n\t\t\tquery,\r\n\t\t\tsort,\r\n\t\t\tpage,\r\n\t\t\tpages,\r\n\t\t\tperPage,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Push book to books array.\r\n\t * @param {Book} book Book.\r\n\t * @returns {boolean} Whatever was book added or not.\r\n\t * @private\r\n\t */\r\n\tpushBook(book) {\r\n\t\tif (book instanceof Book) {\r\n\t\t\tthis.books.push(book);\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Request next page.\r\n\t * @throws Error if search request can't be paginated.\r\n\t * @throws Error if `api` is missing as instance property or function argument.\r\n\t * @param {API} [api=this.api] API instance.\r\n\t * @returns {Promise<Search>} Next page search.\r\n\t */\r\n\tgetNextPage(api = this.api) {\r\n\t\tlet { query, page, sort, } = this;\r\n\t\tif (query === null)\r\n\t\t\tthrow Error('pagination impossible.');\r\n\t\tif (!(api instanceof API))\r\n\t\t\tthrow Error('api must exists.');\r\n\t\treturn query instanceof Tag\r\n\t\t\t? api.searchTagged(query, page + 1, sort)\r\n\t\t\t: api.search(query, page + 1, sort);\r\n\t}\r\n}\r\n\r\nexport {\r\n\tSearch,\r\n\tSearchSort,\r\n};\r\n", "// eslint-disable-next-line no-unused-vars\r\nimport { IncomingMessage, } from 'http';\r\n\r\n\r\nclass APIError extends Error {\r\n\t/**\r\n\t * Original error.\r\n\t * @type {?Error}\r\n\t * @default null\r\n\t */\r\n\toriginalError = null;\r\n\r\n\t/**\r\n\t * HTTP response.\r\n\t * @type {IncomingMessage}\r\n\t * @default null\r\n\t */\r\n\thttpResponse = null;\r\n\r\n\t/**\r\n\t * Error message.\r\n\t * @param {string} message Message.\r\n\t */\r\n\tconstructor(message = 'Unknown error') {\r\n\t\tsuper(message);\r\n\t}\r\n\r\n\t/**\r\n\t * Absorb error.\r\n\t * @param {Error} [error=null] Original error.\r\n\t * @param {?IncomingMessage} [httpResponse=null] HTTP response.\r\n\t * @returns {APIError}\r\n\t */\r\n\tstatic absorb(error, httpResponse = null) {\r\n\t\treturn Object.assign(new APIError(error.message), {\r\n\t\t\toriginalError: error,\r\n\t\t\thttpResponse,\r\n\t\t});\r\n\t}\r\n}\r\n\r\nexport default APIError;\r\n", "/**\r\n * @module API\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./options\").nHentaiOptions } nHentaiOptions\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./options\").nHentaiHosts } nHentaiHosts\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./options\").httpAgent } httpAgent\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./search\").SearchSortMode } SearchSortMode\r\n */\r\n\r\n// eslint-disable-next-line no-unused-vars\r\nimport http, { IncomingMessage, } from 'http';\r\nimport https from 'https';\r\n\r\nimport { version, } from '../package.json';\r\n\r\nimport Book from './book';\r\nimport APIError from './error';\r\nimport Image from './image';\r\nimport processOptions from './options';\r\nimport { Search, } from './search';\r\nimport { Tag, } from './tag';\r\n\r\n\r\n/**\r\n * API arguments\r\n * @typedef {object} APIArgs\r\n * @property {string}   host    API host.\r\n * @property {Function} apiPath API endpoint URL path generator.\r\n */\r\n\r\n/**\r\n * Class used for building URL paths to nHentai API endpoints.\r\n * This class is internal and has only static methods.\r\n * @class\r\n */\r\nclass APIPath {\r\n\t/**\r\n\t * Search by query endpoint.\r\n\t * @param {string}          query     Search query.\r\n\t * @param {?number}         [page=1]  Page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic search(query, page = 1, sort = '') {\r\n\t\treturn `/api/galleries/search?query=${query}&page=${page}${sort ? '&sort=' + sort : ''}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Search by tag endpoint.\r\n\t * @param {number}  tagID    Tag ID.\r\n\t * @param {?number} [page=1] Page ID.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic searchTagged(tagID, page = 1) {\r\n\t\treturn `/api/galleries/tagged?tag_id=${tagID}&page=${page}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Search alike endpoint.\r\n\t * @param {number} bookID Book ID.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic searchAlike(bookID) {\r\n\t\treturn `/api/gallery/${bookID}/related`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book content endpoint.\r\n\t * @param {number} bookID Book ID.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic book(bookID) {\r\n\t\treturn `/api/gallery/${bookID}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book's cover image endpoint.\r\n\t * @param {number} mediaID   Media ID.\r\n\t * @param {string} extension Image extension.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic bookCover(mediaID, extension) {\r\n\t\treturn `/galleries/${mediaID}/cover.${extension}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book's page image endpoint.\r\n\t * @param {number} mediaID   Media ID.\r\n\t * @param {number} page      Page ID.\r\n\t * @param {string} extension Image extension.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic bookPage(mediaID, page, extension) {\r\n\t\treturn `/galleries/${mediaID}/${page}.${extension}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book's page's thumbnail image endpoint.\r\n\t * @param {number} mediaID   Media ID.\r\n\t * @param {number} page      Page ID.\r\n\t * @param {string} extension Image extension.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic bookThumb(mediaID, page, extension) {\r\n\t\treturn `/galleries/${mediaID}/${page}t.${extension}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Redirect to random book at website.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic randomBookRedirect() {\r\n\t\treturn '/random/';\r\n\t}\r\n}\r\n\r\n/**\r\n * Class used for interaction with nHentai API.\r\n * @class\r\n */\r\nclass API {\r\n\t/**\r\n\t * API path class\r\n\t * @type {APIPath}\r\n\t * @static\r\n\t * @private\r\n\t */\r\n\tstatic APIPath = APIPath;\r\n\r\n\t/**\r\n\t * Hosts\r\n\t * @type {?nHentaiHosts}\r\n\t */\r\n\thosts;\r\n\r\n\t/**\r\n\t * Prefer HTTPS over HTTP.\r\n\t * @type {?boolean}\r\n\t */\r\n\tssl;\r\n\r\n\t/**\r\n\t * HTTP(S) agent.\r\n\t * @property {?httpAgent}\r\n\t */\r\n\tagent;\r\n\r\n\t/**\r\n\t * Cookies string.\r\n\t * @type {?string}\r\n\t */\r\n\tcookies;\r\n\r\n\t/**\r\n\t * Use Puppeteer with stealth plugin instead of native HTTP requests.\r\n\t * @type {?boolean}\r\n\t */\r\n\tusePuppeteer;\r\n\r\n\t/**\r\n\t * Additional arguments to pass to Puppeteer browser launch.\r\n\t * @type {?string[]}\r\n\t */\r\n\tbrowserArgs;\r\n\r\n\t/**\r\n\t * Applies provided options on top of defaults.\r\n\t * @param {?nHentaiOptions} [options={}] Options to apply.\r\n\t */\r\n\tconstructor(options = {}) {\r\n\t\tlet params = processOptions(options);\r\n\r\n\t\tObject.assign(this, params);\r\n\t}\r\n\r\n\t/**\r\n\t * Get http(s) module depending on `options.ssl`.\r\n\t * @type {https|http}\r\n\t */\r\n\tget net() {\r\n\t\treturn this.ssl\r\n\t\t\t? https\r\n\t\t\t: http;\r\n\t}\r\n\r\n\t/**\r\n\t * Select a host from an array of hosts using round-robin.\r\n\t * @param {string[]} hosts Array of hosts.\r\n\t * @param {string} [fallback] Fallback host if array is empty.\r\n\t * @returns {string} Selected host.\r\n\t * @private\r\n\t */\r\n\tselectHost(hosts, fallback = 'nhentai.net') {\r\n\t\tif (!Array.isArray(hosts) || hosts.length === 0) {\r\n\t\t\treturn fallback;\r\n\t\t}\r\n\r\n\t\t// Simple round-robin selection based on current time\r\n\t\tconst index = Math.floor(Math.random() * hosts.length);\r\n\t\treturn hosts[index];\r\n\t}\r\n\r\n\t/**\r\n\t * JSON get request.\r\n\t * @param {object} options      HTTP(S) request options.\r\n\t * @param {string} options.host Host.\r\n\t * @param {string} options.path Path.\r\n\t * @returns {Promise<object>} Parsed JSON.\r\n\t */\r\n\trequest(options) {\r\n\t\t// Use Puppeteer if enabled\r\n\t\tif (this.usePuppeteer) {\r\n\t\t\treturn this.requestWithPuppeteer(options);\r\n\t\t}\r\n\r\n\t\t// Use native HTTP requests\r\n\t\tlet {\r\n\t\t\tnet,\r\n\t\t\tagent,\r\n\t\t\tcookies,\r\n\t\t} = this;\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tconst headers = {\r\n\t\t\t\t'User-Agent': `nhentai-api-client/${version} Node.js/${process.versions.node}`,\r\n\t\t\t};\r\n\r\n\t\t\t// Add cookies if provided\r\n\t\t\tif (cookies) {\r\n\t\t\t\theaders.Cookie = cookies;\r\n\t\t\t}\r\n\r\n\t\t\tObject.assign(options, {\r\n\t\t\t\tagent,\r\n\t\t\t\theaders,\r\n\t\t\t});\r\n\r\n\t\t\tnet.get(options, _response => {\r\n\t\t\t\tconst\r\n\t\t\t\t\t/** @type {IncomingMessage}*/\r\n\t\t\t\t\tresponse = _response,\r\n\t\t\t\t\t{ statusCode, } = response,\r\n\t\t\t\t\tcontentType = response.headers['content-type'];\r\n\r\n\t\t\t\tlet error;\r\n\t\t\t\tif (statusCode !== 200)\r\n\t\t\t\t\terror = new Error(`Request failed with status code ${statusCode}`);\r\n\t\t\t\telse if (!(/^application\\/json/).test(contentType))\r\n\t\t\t\t\terror = new Error(`Invalid content-type - expected application/json but received ${contentType}`);\r\n\r\n\t\t\t\tif (error) {\r\n\t\t\t\t\tresponse.resume();\r\n\t\t\t\t\treject(APIError.absorb(error, response));\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tresponse.setEncoding('utf8');\r\n\t\t\t\tlet rawData = '';\r\n\t\t\t\tresponse.on('data', (chunk) => rawData += chunk);\r\n\t\t\t\tresponse.on('end', () => {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tresolve(JSON.parse(rawData));\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\treject(APIError.absorb(error, response));\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}).on('error', error => reject(APIError.absorb(error)));\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * JSON get request using Puppeteer with stealth plugin.\r\n\t * @param {object} options      HTTP(S) request options.\r\n\t * @param {string} options.host Host.\r\n\t * @param {string} options.path Path.\r\n\t * @returns {Promise<object>} Parsed JSON.\r\n\t * @private\r\n\t */\r\n\tasync requestWithPuppeteer(options) {\r\n\t\tlet puppeteer, StealthPlugin;\r\n\r\n\t\ttry {\r\n\t\t\t// Dynamic import to avoid requiring puppeteer when not needed\r\n\t\t\tpuppeteer = await import('puppeteer-extra');\r\n\t\t\tStealthPlugin = (await import('puppeteer-extra-plugin-stealth')).default;\r\n\t\t} catch (error) {\r\n\t\t\tthrow new Error('Puppeteer dependencies not found. Please install puppeteer-extra and puppeteer-extra-plugin-stealth: npm install puppeteer-extra puppeteer-extra-plugin-stealth');\r\n\t\t}\r\n\r\n\t\t// Use stealth plugin\r\n\t\tpuppeteer.default.use(StealthPlugin());\r\n\r\n\t\tconst url = `http${this.ssl ? 's' : ''}://${options.host}${options.path}`;\r\n\t\tlet browser;\r\n\r\n\t\ttry {\r\n\t\t\t// Launch browser with provided arguments\r\n\t\t\tbrowser = await puppeteer.default.launch({\r\n\t\t\t\theadless: 'new',\r\n\t\t\t\targs    : this.browserArgs || [],\r\n\t\t\t});\r\n\r\n\t\t\tconst page = await browser.newPage();\r\n\r\n\t\t\t// Set user agent\r\n\t\t\tawait page.setUserAgent(`nhentai-api-client/${version} Node.js/${process.versions.node}`);\r\n\r\n\t\t\t// Set cookies if provided\r\n\t\t\tif (this.cookies) {\r\n\t\t\t\tconst cookieStrings = this.cookies.split(';'),\r\n\t\t\t\t\tcookies = cookieStrings.map(cookieStr => {\r\n\t\t\t\t\t\tconst [ name, value, ] = cookieStr.trim().split('=');\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tname  : name.trim(),\r\n\t\t\t\t\t\t\tvalue : value ? value.trim() : '',\r\n\t\t\t\t\t\t\tdomain: options.host,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t});\r\n\t\t\t\tawait page.setCookie(...cookies);\r\n\t\t\t}\r\n\r\n\t\t\t// Navigate to the URL\r\n\t\t\tconst response = await page.goto(url, {\r\n\t\t\t\twaitUntil: 'networkidle0',\r\n\t\t\t\ttimeout  : 30000,\r\n\t\t\t});\r\n\r\n\t\t\tif (!response.ok()) {\r\n\t\t\t\tthrow new Error(`Request failed with status code ${response.status()}`);\r\n\t\t\t}\r\n\r\n\t\t\t// Get the response text\r\n\t\t\tconst content = await page.content(),\r\n\t\t\t\tjsonMatch = content.match(/<pre[^>]*>(.*?)<\\/pre>/s);\r\n\t\t\tlet jsonText;\r\n\r\n\t\t\tif (jsonMatch) {\r\n\t\t\t\t// Extract JSON from <pre> tag (common for API responses)\r\n\t\t\t\tjsonText = jsonMatch[1].trim();\r\n\t\t\t} else {\r\n\t\t\t\t// Try to get JSON from page.evaluate\r\n\t\t\t\tjsonText = await page.evaluate(() => {\r\n\t\t\t\t\t// Try to find JSON in the page\r\n\t\t\t\t\t// eslint-disable-next-line no-undef\r\n\t\t\t\t\tconst preElement = document.querySelector('pre');\r\n\t\t\t\t\tif (preElement) {\r\n\t\t\t\t\t\treturn preElement.textContent;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// If no pre element, return the whole body text\r\n\t\t\t\t\t// eslint-disable-next-line no-undef\r\n\t\t\t\t\treturn document.body.textContent;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\treturn JSON.parse(jsonText);\r\n\t\t\t} catch (parseError) {\r\n\t\t\t\tthrow new Error(`Invalid JSON response: ${parseError.message}`);\r\n\t\t\t}\r\n\r\n\t\t} finally {\r\n\t\t\tif (browser) {\r\n\t\t\t\tawait browser.close();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Get API arguments.\r\n\t * This is internal method.\r\n\t * @param {string} hostType Host type.\r\n\t * @param {string} api      Endpoint type.\r\n\t * @returns {APIArgs} API arguments.\r\n\t * @private\r\n\t */\r\n\tgetAPIArgs(hostType, api) {\r\n\t\tlet {\r\n\t\t\thosts: {\r\n\t\t\t\t[hostType]: hostConfig,\r\n\t\t\t},\r\n\t\t\tconstructor: {\r\n\t\t\t\tAPIPath: {\r\n\t\t\t\t\t[api]: apiPath,\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t} = this;\r\n\r\n\t\t// Select host from array or use single host\r\n\t\tconst host = Array.isArray(hostConfig)\r\n\t\t\t? this.selectHost(hostConfig, hostConfig[0])\r\n\t\t\t: hostConfig;\r\n\r\n\t\treturn {\r\n\t\t\thost,\r\n\t\t\tapiPath,\r\n\t\t};\r\n\t}\r\n\r\n\t/**\r\n\t * Search by query.\r\n\t * @param {string}          query     Query.\r\n\t * @param {?number}         [page=1]  Page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @returns {Promise<Search>} Search instance.\r\n\t * @async\r\n\t */\r\n\tasync search(query, page = 1, sort = '') {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'search'),\r\n\t\t\tsearch = Search.parse(\r\n\t\t\t\tawait this.request({\r\n\t\t\t\t\thost,\r\n\t\t\t\t\tpath: apiPath(query, page, sort),\r\n\t\t\t\t})\r\n\t\t\t);\r\n\r\n\t\tObject.assign(search, {\r\n\t\t\tapi: this,\r\n\t\t\tquery,\r\n\t\t\tpage,\r\n\t\t\tsort,\r\n\t\t});\r\n\r\n\t\treturn search;\r\n\t}\r\n\r\n\t/**\r\n\t * Search by query.\r\n\t * @param {string}          query     Query.\r\n\t * @param {?number}         [page=1]  Starting page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @yields {Search} Search instance.\r\n\t * @async\r\n\t * @returns {AsyncGenerator<Search, Search, Search>}\r\n\t */\r\n\tasync * searchGenerator(query, page = 1, sort = '') {\r\n\t\tlet search = await this.search(query, page, sort);\r\n\r\n\t\twhile (search.page <= search.pages) {\r\n\t\t\tyield search;\r\n\t\t\tsearch = await this.search(query, search.page + 1, sort);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Search related books.\r\n\t * @param {number|Book} book Book instance or Book ID.\r\n\t * @returns {Promise<Search>} Search instance.\r\n\t * @async\r\n\t */\r\n\tasync searchAlike(book) {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'searchAlike');\r\n\r\n\t\treturn Search.parse(\r\n\t\t\tawait this.request({\r\n\t\t\t\thost,\r\n\t\t\t\tpath: apiPath(\r\n\t\t\t\t\tbook instanceof Book\r\n\t\t\t\t\t\t? book.id\r\n\t\t\t\t\t\t: +book\r\n\t\t\t\t),\r\n\t\t\t})\r\n\t\t);\r\n\t}\r\n\r\n\t/**\r\n\t * Search by tag id.\r\n\t * @param {number|Tag}      tag       Tag or Tag ID.\r\n\t * @param {?number}         [page=1]  Page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @returns {Promise<Search>} Search instance.\r\n\t * @async\r\n\t */\r\n\tasync searchTagged(tag, page = 1, sort = '') {\r\n\t\tif (!(tag instanceof Tag))\r\n\t\t\ttag = Tag.get({ id: +tag, });\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'searchTagged'),\r\n\t\t\tsearch = Search.parse(\r\n\t\t\t\tawait this.request({\r\n\t\t\t\t\thost,\r\n\t\t\t\t\tpath: apiPath(tag.id, page, sort),\r\n\t\t\t\t})\r\n\t\t\t);\r\n\r\n\t\tObject.assign(search, {\r\n\t\t\tapi  : this,\r\n\t\t\tquery: tag,\r\n\t\t\tpage,\r\n\t\t\tsort,\r\n\t\t});\r\n\r\n\t\treturn search;\r\n\t}\r\n\r\n\t/**\r\n\t * Get book by id.\r\n\t * @param {number} bookID Book ID.\r\n\t * @returns {Promise<Book>} Book instance.\r\n\t * @async\r\n\t */\r\n\tasync getBook(bookID) {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'book');\r\n\r\n\t\treturn Book.parse(\r\n\t\t\tawait this.request({\r\n\t\t\t\thost,\r\n\t\t\t\tpath: apiPath(bookID),\r\n\t\t\t})\r\n\t\t);\r\n\t}\r\n\r\n\t/**\r\n\t * Get random book.\r\n\t * @returns {Promise<Book>} Book instance.\r\n\t * @async\r\n\t */\r\n\tasync getRandomBook() {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'randomBookRedirect');\r\n\r\n\t\ttry {\r\n\t\t\tawait this.request({\r\n\t\t\t\thost,\r\n\t\t\t\tpath: apiPath(),\r\n\t\t\t}); // Will always throw\r\n\t\t} catch (error) {\r\n\t\t\tif (!(error instanceof APIError))\r\n\t\t\t\tthrow error;\r\n\t\t\tconst response = error.httpResponse;\r\n\t\t\tif (!response || response.statusCode !== 302)\r\n\t\t\t\tthrow error;\r\n\t\t\tconst id = +((/\\d+/).exec(response.headers.location) || {})[0];\r\n\t\t\tif (isNaN(id))\r\n\t\t\t\tthrow APIError.absorb(new Error('Bad redirect'), response);\r\n\t\t\treturn await this.getBook(id);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Detect the actual cover filename extension for nhentai's double extension format.\r\n\t * @param {Image} image Cover image.\r\n\t * @returns {string} The actual extension to use in the URL.\r\n\t * @private\r\n\t */\r\n\tdetectCoverExtension(image) {\r\n\t\tconst reportedExtension = image.type.extension;\r\n\r\n\t\t// Handle WebP cases - both simple and double extension formats\r\n\t\tif (reportedExtension === 'webp') {\r\n\t\t\t// Some WebP files also have double extensions like cover.webp.webp\r\n\t\t\t// We need to detect this based on media ID or other patterns\r\n\r\n\t\t\t// For now, we'll try the double WebP format for certain media ID ranges\r\n\t\t\t// This is based on observation that newer uploads tend to have cover.webp.webp\r\n\t\t\tconst mediaId = image.book.media;\r\n\r\n\t\t\t// Media IDs above ~3000000 seem to use cover.webp.webp format\r\n\t\t\t// This is a heuristic that may need adjustment based on more data\r\n\t\t\tif (mediaId > 3000000) {\r\n\t\t\t\treturn 'webp.webp';\r\n\t\t\t}\r\n\r\n\t\t\t// Default to simple webp for older uploads\r\n\t\t\treturn 'webp';\r\n\t\t}\r\n\r\n\t\t// For non-webp extensions, nhentai often serves double extensions\r\n\t\t// The pattern is: cover.{original_extension}.webp\r\n\t\t// We need to detect what the original extension should be\r\n\r\n\t\t// Map API type codes to likely intermediate extensions\r\n\t\tconst intermediateExtensionMap = {\r\n\t\t\t\t'jpg' : 'jpg',    // API reports 'j' -> likely cover.jpg.webp\r\n\t\t\t\t'jpeg': 'jpg',    // API reports 'jpeg' -> likely cover.jpg.webp\r\n\t\t\t\t'png' : 'png',    // API reports 'p' -> likely cover.png.webp\r\n\t\t\t\t'gif' : 'gif',    // API reports 'g' -> likely cover.gif.webp\r\n\t\t\t},\r\n\t\t\tintermediateExt = intermediateExtensionMap[reportedExtension];\r\n\r\n\t\tif (intermediateExt) {\r\n\t\t\t// Return double extension format: original.webp\r\n\t\t\treturn `${intermediateExt}.webp`;\r\n\t\t}\r\n\r\n\t\t// Fallback to reported extension if we can't map it\r\n\t\treturn reportedExtension;\r\n\t}\r\n\r\n\t/**\r\n\t * Get image URL.\r\n\t * @param {Image} image Image.\r\n\t * @returns {string} Image URL.\r\n\t */\r\n\tgetImageURL(image) {\r\n\t\tif (image instanceof Image) {\r\n\t\t\tlet { host, apiPath, } = image.isCover\r\n\t\t\t\t\t? this.getAPIArgs('thumbs', 'bookCover')\r\n\t\t\t\t\t: this.getAPIArgs('images', 'bookPage'),\r\n\t\t\t\textension;\r\n\r\n\t\t\t// Handle cover images with potential double extensions\r\n\t\t\tif (image.isCover) {\r\n\t\t\t\textension = this.detectCoverExtension(image);\r\n\t\t\t} else {\r\n\t\t\t\t// Regular pages use simple extensions\r\n\t\t\t\textension = image.type.extension;\r\n\t\t\t}\r\n\r\n\t\t\treturn `http${this.ssl ? 's' : ''}://${host}` + (image.isCover\r\n\t\t\t\t? apiPath(image.book.media, extension)\r\n\t\t\t\t: apiPath(image.book.media, image.id, extension));\r\n\t\t}\r\n\t\tthrow new Error('image must be Image instance.');\r\n\t}\r\n\r\n\t/**\r\n\t * Get image URL with original extension (fallback for when double extension fails).\r\n\t * @param {Image} image Image.\r\n\t * @returns {string} Image URL with original extension.\r\n\t */\r\n\tgetImageURLOriginal(image) {\r\n\t\tif (image instanceof Image) {\r\n\t\t\tlet { host, apiPath, } = image.isCover\r\n\t\t\t\t? this.getAPIArgs('thumbs', 'bookCover')\r\n\t\t\t\t: this.getAPIArgs('images', 'bookPage');\r\n\r\n\t\t\t// Always use the original extension reported by the API\r\n\t\t\treturn `http${this.ssl ? 's' : ''}://${host}` + (image.isCover\r\n\t\t\t\t? apiPath(image.book.media, image.type.extension)\r\n\t\t\t\t: apiPath(image.book.media, image.id, image.type.extension));\r\n\t\t}\r\n\t\tthrow new Error('image must be Image instance.');\r\n\t}\r\n\r\n\t/**\r\n\t * Get all possible cover image URL variants for testing.\r\n\t * @param {Image} image Cover image.\r\n\t * @returns {string[]} Array of possible URLs to try.\r\n\t */\r\n\tgetCoverURLVariants(image) {\r\n\t\tif (!(image instanceof Image) || !image.isCover) {\r\n\t\t\tthrow new Error('image must be a cover Image instance.');\r\n\t\t}\r\n\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('thumbs', 'bookCover'),\r\n\t\t\tbaseURL = `http${this.ssl ? 's' : ''}://${host}`,\r\n\t\t\treportedExt = image.type.extension,\r\n\t\t\tvariants = [],\r\n\t\t\t// Add the smart detection URL (our primary method)\r\n\t\t\tsmartExt = this.detectCoverExtension(image);\r\n\r\n\t\tvariants.push(baseURL + apiPath(image.book.media, smartExt));\r\n\r\n\t\t// Add original extension URL\r\n\t\tvariants.push(baseURL + apiPath(image.book.media, reportedExt));\r\n\r\n\t\t// For WebP, add both simple and double variants\r\n\t\tif (reportedExt === 'webp') {\r\n\t\t\tvariants.push(baseURL + apiPath(image.book.media, 'webp'));\r\n\t\t\tvariants.push(baseURL + apiPath(image.book.media, 'webp.webp'));\r\n\t\t}\r\n\r\n\t\t// For non-WebP, add the double extension variant\r\n\t\tif (reportedExt !== 'webp') {\r\n\t\t\tvariants.push(baseURL + apiPath(image.book.media, `${reportedExt}.webp`));\r\n\t\t}\r\n\r\n\t\t// Remove duplicates\r\n\t\treturn [ ...new Set(variants), ];\r\n\t}\r\n\r\n\t/**\r\n\t * Get image thumbnail URL.\r\n\t * @param {Image} image Image.\r\n\t * @returns {string} Image thumbnail URL.\r\n\t */\r\n\tgetThumbURL(image) {\r\n\t\tif (image instanceof Image && !image.isCover) {\r\n\t\t\tlet { host, apiPath, } = this.getAPIArgs('thumbs', 'bookThumb');\r\n\r\n\t\t\treturn `http${this.ssl ? 's' : ''}://${host}`\r\n\t\t\t\t+ apiPath(image.book.media, image.id, image.type.extension);\r\n\t\t}\r\n\t\tthrow new Error('image must be Image instance and not book cover.');\r\n\t}\r\n}\r\n\r\nexport default API;\r\n", "import { Agent, } from 'http';\r\nimport { Agent as SSLAgent, } from 'https';\r\n\r\n\r\n/**\r\n * Agent-like object or Agent class or it's instance.\r\n * @global\r\n * @typedef {object|Agent|SSLAgent} httpAgent\r\n */\r\n\r\n/**\r\n * Common nHentai API hosts object.\r\n * @global\r\n * @typedef {object} nHentaiHosts\r\n * @property {?string}         api    Main API host.\r\n * @property {?string|string[]} images Media API host(s). Can be a single host or array of hosts for load balancing.\r\n * @property {?string|string[]} thumbs Media thumbnails API host(s). Can be a single host or array of hosts for load balancing.\r\n */\r\n\r\n/**\r\n * Common nHentai options object.\r\n * @global\r\n * @typedef {object} nHentaiOptions\r\n * @property {?nHentaiHosts} hosts         Hosts.\r\n * @property {?boolean}      ssl           Prefer HTTPS over HTTP.\r\n * @property {?httpAgent}    agent         HTTP(S) agent.\r\n * @property {?string}       cookies       Cookies string in format 'cookie1=value1;cookie2=value2;...'\r\n * @property {?boolean}      usePuppeteer  Use Puppeteer with stealth plugin instead of native HTTP requests.\r\n * @property {?string[]}     browserArgs   Additional arguments to pass to Puppeteer browser launch.\r\n */\r\n\r\n/**\r\n * Applies provided options on top of defaults.\r\n * @param {?nHentaiOptions} [options={}] Options to apply.\r\n * @returns {nHentaiOptions} Unified options.\r\n */\r\nfunction processOptions({\r\n\thosts: {\r\n\t\tapi    = 'nhentai.net',\r\n\t\timages = [\r\n\t\t\t'i1.nhentai.net',\r\n\t\t\t'i2.nhentai.net',\r\n\t\t\t'i3.nhentai.net',\r\n\t\t],\r\n\t\tthumbs = [\r\n\t\t\t't1.nhentai.net',\r\n\t\t\t't2.nhentai.net',\r\n\t\t\t't3.nhentai.net',\r\n\t\t],\r\n\t} = {},\r\n\tssl          = true,\r\n\tagent        = null,\r\n\tcookies      = null,\r\n\tusePuppeteer = false,\r\n\tbrowserArgs  = [],\r\n} = {}) {\r\n\tif (!agent)\r\n\t\tagent = ssl\r\n\t\t\t? SSLAgent\r\n\t\t\t: Agent;\r\n\r\n\tif (agent.constructor.name === 'Function')\r\n\t\tagent = new agent();\r\n\r\n\t// Normalize hosts to arrays for consistent handling\r\n\tconst normalizeHosts = (hostConfig) => {\r\n\t\tif (typeof hostConfig === 'string') {\r\n\t\t\treturn [ hostConfig, ];\r\n\t\t}\r\n\t\treturn Array.isArray(hostConfig) ? hostConfig : [ hostConfig, ];\r\n\t};\r\n\r\n\treturn {\r\n\t\thosts: {\r\n\t\t\tapi,\r\n\t\t\timages: normalizeHosts(images),\r\n\t\t\tthumbs: normalizeHosts(thumbs),\r\n\t\t},\r\n\t\tssl,\r\n\t\tagent,\r\n\t\tcookies,\r\n\t\tusePuppeteer,\r\n\t\tbrowserArgs,\r\n\t};\r\n}\r\n\r\nexport default processOptions;\r\n", "import API from './api';\r\nimport Book from './book';\r\nimport Image from './image';\r\nimport { Search, SearchSort, } from './search';\r\nimport { Tag, } from './tag';\r\n\r\n\r\nexport {\r\n\tAPI,\r\n\tSearch,\r\n\tSearchSort,\r\n\tBook,\r\n\tImage,\r\n\tTag,\r\n};\r\n\r\n/**\r\n * @typedef { import(\"./tag\").TagTypes } TagTypes\r\n */\r\n\r\n/**\r\n * @type {TagTypes}\r\n */\r\nexport const TagTypes = {\r\n\tUnknown  : Tag.types.Unknown,\r\n\tTag      : Tag.types.Tag,\r\n\tCategory : Tag.types.Category,\r\n\tArtist   : Tag.types.Artist,\r\n\tParody   : Tag.types.Parody,\r\n\tCharacter: Tag.types.Character,\r\n\tGroup    : Tag.types.Group,\r\n\tLanguage : Tag.types.Language,\r\n};\r\n"], "names": ["TagType", "constructor", "type", "knownTypes", "this", "isKnown", "UnknownTagType", "toString", "Tag", "tag", "id", "name", "count", "url", "types", "Unknown", "Object", "assign", "get", "compare", "strict", "map", "prop", "reduce", "accum", "current", "includeCount", "Category", "Artist", "<PERSON><PERSON><PERSON>", "Character", "Group", "Language", "known", "toLowerCase", "ImageType", "extension", "UnknownImageType", "Image", "image", "t", "w", "width", "h", "height", "JPEG", "book", "Book", "isCover", "filename", "PNG", "GIF", "WEBP", "TagsArray", "Array", "args", "toNames", "from", "title", "media", "media_id", "favorites", "num_favorites", "scanlator", "uploaded", "Date", "upload_date", "tags", "cover", "parse", "images", "pages", "english", "japanese", "pretty", "setCover", "isArray", "for<PERSON>ach", "pushPage", "bind", "pushTag", "UnknownBook", "page", "push", "hasTag", "some", "elem", "hasTagWith", "getTagsWith", "filter", "pureTags", "TagTypes", "categories", "artists", "parodies", "characters", "groups", "languages", "SearchSort", "Search", "search", "num_pages", "perPage", "per_page", "result", "length", "books", "query", "sort", "pushBook", "getNextPage", "api", "Error", "API", "searchTagged", "APIError", "message", "error", "httpResponse", "originalError", "options", "params", "processOptions", "hosts", "thumbs", "ssl", "agent", "cookies", "usePuppeteer", "browserArgs", "SSLAgent", "Agent", "normalizeHosts", "hostConfig", "net", "https", "http", "selectHost", "fallback", "Math", "floor", "random", "request", "requestWithPuppeteer", "Promise", "resolve", "reject", "headers", "process", "versions", "node", "<PERSON><PERSON>", "_response", "response", "statusCode", "contentType", "test", "resume", "absorb", "setEncoding", "rawData", "on", "chunk", "JSON", "puppeteer", "StealthPlugin", "import", "default", "use", "host", "path", "browser", "launch", "headless", "newPage", "setUserAgent", "split", "cookieStr", "value", "trim", "domain", "<PERSON><PERSON><PERSON><PERSON>", "goto", "waitUntil", "timeout", "ok", "status", "jsonMatch", "content", "match", "jsonText", "evaluate", "preElement", "document", "querySelector", "textContent", "body", "parseError", "close", "getAPIArgs", "hostType", "APIPath", "<PERSON><PERSON><PERSON><PERSON>", "bookID", "exec", "location", "isNaN", "getBook", "detectCoverExtension", "reportedExtension", "intermediateExt", "getImageURL", "getImageURLOriginal", "getCoverURLVariants", "baseURL", "reportedExt", "variants", "smartExt", "Set", "getThumbURL", "tagID", "mediaID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BA,MAAMA;;;;;;;;;;;;;;;AAmBLC,YAAYC,kCANL,MAOFA,YACEA,KAAOA,UACPD,YAAYE,WAAWD,MAAQE;;;;KAQlCC,sBACMD,gBAAgBE;;;;KAO1BC,kBACQH,KAAKF;;;;;mBAvCRF,qBAMe,IA0CrB,MAAMM,uBAAuBN;;;;;AAK5BC,YAAYC,KAAO,iBACZ,WACDA,KAAOA;;;;GAQd,MAAMM;;;;;;;;;;;;WA2CMC,YACJA,eAAeL,OACpBK,IAAM,IAAIL,KAAK,CACdM,IAAQD,IAAIC,GACZR,KAAOO,IAAIP,KACXS,KAAOF,IAAIE,KACXC,OAAQH,IAAIG,MACZC,IAAOJ,IAAII,OAENJ;;;;;;;;;;;;;;KA+CRR,aAAYS,GACXA,GAAQ,EADGR,KAEXA,KAAQE,KAAKH,YAAYa,MAAMC,QAFpBJ,KAGXA,KAAQ,GAHGC,MAIXA,MAAQ,EAJGC,IAKXA,IAAQ,IACL,8BA7CC,+BAOET,KAAKH,YAAYa,MAAMC,qCAOvB,iCAOC,8BAOF,IAkBLC,OAAOC,OAAOb,KAAM,CACnBM,GAAAA,GACAR,KAAMA,gBAAgBF,QACnBE,KACAE,KAAKH,YAAYa,MAAMI,IAAIhB,MAC9BS,KAAAA,KACAC,MAAAA,MACAC,IAAAA;;;;;;;;KAYFM,QAAQV,IAAKW,QAAS,MACrBX,IAAML,KAAKH,YAAYiB,IAAIT,KACZ,QAAXW,OACHA,QAAS,OACL,GAAIhB,KAAKM,KAAOD,IAAIC,GACxB,OAAO,UAEC,CACR,KACA,OACA,OACA,QACA,OACCW,KACDC,MAAQb,IAAIa,QAAUlB,KAAKkB,QAC1BC,QACD,CAACC,MAAOC,UAAYL,OACjBI,MAAQC,QACRD,MAAQC;;;;;KASblB,SAASmB,cAAe,UAChBtB,KAAKO,MAAQe,aAChB,KAAItB,KAAKQ,SACV,qBA3JCJ,YAMU,CACdO,QAAW,IAAIT;;AACfE,IAAW,IAAIR,QAAQ,OACvB2B,SAAW,IAAI3B,QAAQ,YACvB4B,OAAW,IAAI5B,QAAQ,UACvB6B,OAAW,IAAI7B,QAAQ,UACvB8B,UAAW,IAAI9B,QAAQ,aACvB+B,MAAW,IAAI/B,QAAQ,SACvBgC,SAAW,IAAIhC,QAAQ;;;;;AAMvBiC,MAAOjC,QAAQG;;;;;;AAOfe,IAAIhB,UACC+B,YACA,iBAAoB/B,OACvBA,KAAOA,KAAKgC,gBACJD,MAAQ7B,KAAK6B,MAAM/B,OACzB+B,MACA,IAAI3B,eAAeJ;;;;;;;;;;;;;;;;;;;ACrGzB,MAAMiC;;;;;;;;;;;;;;;;;;;;;AA2BLlC,YAAYC,KAAMkC,uCAdX,uCAOK,MAQPlC,YACEA,KAAOA,UACPD,YAAYE,WAAWD,MAAQE,WAEhCgC,UAAYA;;;;KAOd/B,sBACMD,gBAAgBiC;;;;KAOtB1B,kBACIP,KAAKF;;;;;mBAhDRiC,uBAMe,IAmDrB,MAAME,yBAAyBF;;;;;;AAM9BlC,YAAYC,KAAMkC,iBACX,KAAMA,gBACPlC,KAAOA;;;;GAQd,MAAMoC;;;;;;;;;;;;;aA8DQC,MAAO7B,GAAK,OAEvB8B,EAAGtC,KACHuC,EAAGC,MACHC,EAAGC,QACAL,aAEG,IAAInC,KAAK,CACfF,KAAAA,KACAwC,OAASA,MACTE,QAASA,OACTlC,GAAAA;;;;;;;;;;;;;;KAgDFT,aAAYS,GACXA,GAAS,EADEgC,MAEXA,MAAS,EAFEE,OAGXA,OAAS,EAHE1C,KAIXA,KAASE,KAAKH,YAAYa,MAAM+B,KAJrBC,KAKXA,KAASC,KAAKhC,SACX,8BA7CC,gCAOG,iCAOC,+BAOFX,KAAKH,YAAYa,MAAM+B,kCAOvBE,KAAKhC,SAkBXC,OAAOC,OAAOb,KAAM,CACnBM,GAAI,iBAAoBA,GACrBA,GAAK,EAAI,EAAIA,GACb,EACHgC,MAAAA,MACAE,OAAAA,OACA1C,KAAMA,gBAAgBiC,UACnBjC,KACAE,KAAKH,YAAYa,MAAMI,IAAIhB,MAC9B4C,KAAMA,gBAAgBC,KACnBD,KACAC,KAAKhC;;;;KAQNiC,qBACI5C,KAAKM,GAAK;;;;KAOduC,qBACK,GAAE7C,KAAK4C,QAAU,QAAU5C,KAAKM,MAAMN,KAAKF,KAAKkC,6BA5JpDE,cAMU,CACdO,KAAM,IAAIV,UAAU,OAAQ,OAC5Be,IAAM,IAAIf,UAAU,MAAO,OAC3BgB,IAAM,IAAIhB,UAAU,MAAO,OAC3BiB,KAAM,IAAIjB,UAAU,OAAQ,QAE5BpB,QAAS,IAAIsB,iBAAiB,UAAW;;;;;AAMzCJ,MAAOE,UAAUhC;;;;;;AAOjBe,IAAIhB,UACC+B,SACA,iBAAoB/B,YACvBA,KAAOA,KAAKgC,mBAEN,QACA,UACA,OACJhC,KAAO,iBAEH,QACA,MACJA,KAAO,gBAEH,QACA,MACJA,KAAO,gBAEH,QACA,OACJA,KAAO,cAID+B,MAAQ7B,KAAK6B,MAAM/B,OACzB+B,MACA,IAAII,iBAAiBnC;;;;;;;AC7I3B,MAAMmD,kBAAkBC,MACvBrD,eAAesD,eACLA;;;;;KAQVC,QAAQ9B,cAAe,UACf4B,MAAMG,KAAKrD,MAAMK,KAAOA,IAAIF,SAASmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GCsB9C,MAAMqB;;;;;;;;;;;;;;;;;aAqBQD,aACL,IAAI1C,KAAK,CACfsD,MAAWZ,KAAKY,MAChBhD,IAAYoC,KAAKpC,GACjBiD,OAAYb,KAAKc,SACjBC,WAAYf,KAAKgB,cACjBC,UAAWjB,KAAKiB,UAChBC,SAAW,IAAIC,KAAyB,KAAnBnB,KAAKoB,aAC1BC,KAAWd,UAAUI,KAAKX,KAAKqB,MAAM1D,KAAOD,IAAIU,IAAIT,OACpD2D,MAAW9B,MAAM+B,MAAMvB,KAAKwB,OAAOF,OACnCG,MAAWzB,KAAKwB,OAAOC,MAAMlD,KAC5B,CAACkB,MAAO7B,KAAO4B,MAAM+B,MAAM9B,QAAS7B;;;;;;;;;;;;;;;;;KAmFvCT,aAAYyD,MACXA,MAAY,CACXc,QAAU,GACVC,SAAU,GACVC,OAAU,IAJAhE,GAMXA,GAAY,EANDiD,MAOXA,MAAY,EAPDE,UAQXA,UAAY,EARDE,UASXA,UAAY,GATDC,SAUXA,SAAY,IAAIC,KAAK,GAVVE,KAWXA,KAAY,IAAId,UAXLe,MAYXA,MAAY,IAAI9B,MAAM,CAAE5B,GAAI,EAAGoC,KAAM1C,OAZ1BmE,MAaXA,MAAY,IACT,iCAxFI,CACPC,QAAU,GACVC,SAAU,GACVC,OAAU,+BAQN,gCAOG,oCAOI,oCAOA,oCAOD,IAAIT,KAAK,gCAOb,IAAIZ,wCAMH,IAAIf,MAAM,CAAE5B,GAAI,EAAGoC,KAAM1C,qCAOzB,SA8BFuE,SAASP,OAEVd,MAAMsB,QAAQL,QACjBA,MAAMM,QAAQzE,KAAK0E,SAASC,KAAK3E,OAE9BkD,MAAMsB,QAAQT,OACjBA,KAAKU,QAAQzE,KAAK4E,QAAQD,KAAK3E,OAEhCY,OAAOC,OAAOb,KAAM,CACnBsD,MAAAA,MACAhD,GAAAA,GACAiD,MAAAA,MACAE,UAAAA,UACAE,UAAAA,UACAC,SAAAA;;;;KAQE3D,sBACMD,gBAAgB6E;;;;;;KAS1BN,SAASP,cACJA,iBAAiB9B,QACpB8B,MAAMtB,KAAO1C,UACRgE,MAAQA,OACN;;;;;;KAWTU,SAASI,aACJA,gBAAgB5C,QACnB4C,KAAKpC,KAAO1C,UACPmE,MAAMY,KAAKD,OACT;;;;;;KAWTF,QAAQvE,YACPA,IAAMD,IAAIU,IAAIT,MAETL,KAAKgF,OAAO3E,YACX0D,KAAKgB,KAAK1E,MACR;;;;;KAUT2E,OAAO3E,IAAKW,QAAS,UACpBX,IAAMD,IAAIU,IAAIT,KAEPL,KAAK+D,KAAKkB,MAAKC,MAAQA,KAAKnE,QAAQV,IAAKW;;;;KAOjDmE,WAAW9E,YACHL,KAAKgF,OAAO3E,IAAK;;;;;KAQzB+E,YAAY/E,YACXA,IAAMD,IAAIU,IAAIT,KAEPL,KAAK+D,KAAKsB,QAAOH,MAAQA,KAAKnE,QAAQV,IAAK;;;;KAO/CiF,sBACItF,KAAKoF,YAAY,CAAEtF,KAAMyF,SAASnF;;;;KAOtCoF,wBACIxF,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAShE;;;;KAOtCkE,qBACIzF,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS/D;;;;KAOtCkE,sBACI1F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS9D;;;;KAOtCkE,wBACI3F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS7D;;;;KAOtCkE,oBACI5F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS5D;;;;KAOtCkE,uBACI7F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS3D;;;;;mBA7RrCe,uCAAAA,2BAsSN,MAAMkC,oBAAoBlC;;;;AAIzB9C,oBACO,KAIR8C,KAAKkC,YAAcA,YACnBlC,KAAKhC,QAAU,IAAIkE;;;;;;;;;;;;ACnUnB,MAAMiB;;;;mBAAAA,oBAIW,oBAJXA,qBAQY,2BARZA,0BAYiB,iCAZjBA,yBAgBgB,gCAhBhBA,0BAoBiB,gBAOvB,MAAMC;;;;;aAKQC,eACL,IAAIhG,KAAK,CACfmE,MAAO6B,OAAOC,WACVD,OAAOC,UACR,EACHC,QAASF,OAAOG,UACZH,OAAOG,SACRH,OAAOI,OAAOC,OACjBC,MAAON,OAAOI,OAAOnF,IAAI0B,KAAKsB,MAAMU,KAAKhC;;;;;;;;;;;;;;;KA+D3C9C,aAAY0G,MACXA,MAAU,KADCC,KAEXA,KAAU,GAFC1B,KAGXA,KAAU,EAHCX,MAIXA,MAAU,EAJC+B,QAKXA,QAAU,EALCI,MAMXA,MAAU,IACP,+BA7DE,mCAOE,kCAOD,gCAOA,kCAOG,gCAOF,iCAOA,GAoBHpD,MAAMsB,QAAQ8B,QACjBA,MAAM7B,QAAQzE,KAAKyG,SAAS9B,KAAK3E,OAElCY,OAAOC,OAAOb,KAAM,CACnBuG,MAAAA,MACAC,KAAAA,KACA1B,KAAAA,KACAX,MAAAA,MACA+B,QAAAA;;;;;;KAUFO,SAAS/D,aACJA,gBAAgBC,YACd2D,MAAMvB,KAAKrC,OACT;;;;;;;KAYTgE,YAAYC,IAAM3G,KAAK2G,SAClBJ,MAAEA,MAAFzB,KAASA,KAAT0B,KAAeA,MAAUxG,QACf,OAAVuG,MACH,MAAMK,MAAM,+BACPD,eAAeE,KACpB,MAAMD,MAAM,2BACNL,iBAAiBnG,IACrBuG,IAAIG,aAAaP,MAAOzB,KAAO,EAAG0B,MAClCG,IAAIX,OAAOO,MAAOzB,KAAO,EAAG0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GC3KjC,MAAMO,iBAAiBH;;;;;;;;;;;;;;;AAmBtB/G,YAAYmH,QAAU,uBACfA,8CAdS,0CAOD;;;;;;mBAgBDC,MAAOC,aAAe,aAC5BtG,OAAOC,OAAO,IAAIkG,SAASE,MAAMD,SAAU,CACjDG,cAAeF,MACfC,aAAAA;;;;;;;;;;;;;;;;AC+FH,MAAML;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDLhH,YAAYuH,QAAU,mPACjBC,OCjJN,SAASC,gBACRC,OAAOZ,IACNA,IAAS,cADHzC,OAENA,OAAS,CACR,iBACA,iBACA,kBALKsD,OAONA,OAAS,CACR,iBACA,iBACA,mBAEE,GAbmBC,IAcvBA,KAAe,EAdQC,MAevBA,MAAe,KAfQC,QAgBvBA,QAAe,KAhBQC,aAiBvBA,cAAe,EAjBQC,YAkBvBA,YAAe,IACZ,IACEH,QACJA,MAAQD,IACLK,MACAC,SAE2B,aAA3BL,MAAM7H,YAAYU,OACrBmH,MAAQ,IAAIA;MAGPM,eAAkBC,YACG,iBAAfA,WACH,CAAEA,YAEH/E,MAAMsB,QAAQyD,YAAcA,WAAa,CAAEA,kBAG5C,CACNV,MAAO,CACNZ,IAAAA,IACAzC,OAAQ8D,eAAe9D,QACvBsD,OAAQQ,eAAeR,SAExBC,IAAAA,IACAC,MAAAA,MACAC,QAAAA,QACAC,aAAAA,aACAC,YAAAA,aDmGaP,CAAeF,SAE5BxG,OAAOC,OAAOb,KAAMqH;;;;KAOjBa,iBACIlI,KAAKyH,IACTU,MACAC;;;;;;;KAUJC,WAAWd,MAAOe,SAAW,mBACvBpF,MAAMsB,QAAQ+C,QAA2B,IAAjBA,MAAMlB,cAC3BiC;OAKDf,MADOgB,KAAKC,MAAMD,KAAKE,SAAWlB,MAAMlB;;;;;;;KAWhDqC,QAAQtB;;GAEHpH,KAAK4H,oBACD5H,KAAK2I,qBAAqBvB;IAI9Bc,IACHA,IADGR,MAEHA,MAFGC,QAGHA,SACG3H,YACG,IAAI4I,SAAQ,CAACC,QAASC,gBACtBC,QAAU,cACA,oCAAwCC,QAAQC,SAASC,kCAIrEvB;UACHoB,QAAQI,OAASxB,SAGlB/G,OAAOC,OAAOuG,QAAS,CACtBM,MAAAA,MACAqB,QAAAA,UAGDb,IAAIpH,IAAIsG,SAASgC;;AAGfC,SAAWD,WACXE,WAAEA,YAAgBD,SAClBE,YAAcF,SAASN,QAAQ,oBAE5B9B,SACe,MAAfqC,WACHrC,MAAQ,IAAIL,MAAO,mCAAkC0C,cAC3C,qBAAsBE,KAAKD,eACrCtC,MAAQ,IAAIL,MAAO,iEAAgE2C,gBAEhFtC,aACHoC,SAASI,cACTX,OAAO/B,SAAS2C,OAAOzC,MAAOoC,WAI/BA,SAASM,YAAY,YACjBC,QAAU,GACdP,SAASQ,GAAG,QAASC,OAAUF,SAAWE,QAC1CT,SAASQ,GAAG,OAAO,SAEjBhB,QAAQkB,KAAK9F,MAAM2F,UAClB,MAAO3C,OACR6B,OAAO/B,SAAS2C,OAAOzC,MAAOoC,kBAG9BQ,GAAG,SAAS5C,OAAS6B,OAAO/B,SAAS2C,OAAOzC;;;;;;;;gCAYtBG,aACtB4C,UAAWC;;AAIdD,gBAAkBE,OAAO,mBACzBD,qBAAuBC,OAAO,mCAAmCC,QAChE,MAAOlD,aACF,IAAIL,MAAM,wLAIjBoD;UAAUG,QAAQC,IAAIH,uBAEhBxJ,IAAO,OAAMT,KAAKyH,IAAM,IAAM,QAAQL,QAAQiD,OAAOjD,QAAQkD,WAC/DC;;AAIHA,cAAgBP,UAAUG,QAAQK,OAAO,CACxCC,SAAU,MACVtH,KAAUnD,KAAK6H,aAAe,WAGzB/C,WAAayF,QAAQG;;SAGrB5F,KAAK6F,aAAc,oCAAwC3B,QAAQC,SAASC,QAG9ElJ,KAAK2H,QAAS,OAEhBA,QADqB3H,KAAK2H,QAAQiD,MAAM,KAChB3J,KAAI4J,kBACnBtK,KAAMuK,OAAWD,UAAUE,OAAOH,MAAM,WACzC,CACNrK,KAAQA,KAAKwK,OACbD,MAAQA,MAAQA,MAAMC,OAAS,GAC/BC,OAAQ5D,QAAQiD,eAGbvF,KAAKmG,aAAatD;MAInB0B,eAAiBvE,KAAKoG,KAAKzK,IAAK,CACrC0K,UAAW,eACXC,QAAW,UAGP/B,SAASgC,WACP,IAAIzE,MAAO,mCAAkCyC,SAASiC;MAK5DC,iBADqBzG,KAAK0G,WACNC,MAAM,+BACvBC;;AAIHA,SAFGH,UAEQA,UAAU,GAAGR,aAGPjG,KAAK6G,UAAS;;;MAGxBC,WAAaC,SAASC,cAAc,cACtCF,WACIA,WAAWG,YAIZF,SAASG,KAAKD;;eAKfhC,KAAK9F,MAAMyH,UACjB,MAAOO,kBACF,IAAIrF,MAAO,0BAAyBqF,WAAWjF,oBAIlDuD,eACGA,QAAQ2B;;;;;;;;KAajBC,WAAWC,SAAUzF,SAEnBY,QACE6E,UAAWnE,YAEbpI,aACCwM,UACE1F,KAAM2F,WAGNtM;MAOG,CACNqK,KALYnH,MAAMsB,QAAQyD,YACxBjI,KAAKqI,WAAWJ,WAAYA,WAAW,IACvCA,WAIFqE,QAAAA;;;;;;;;kBAYW/F,MAAOzB,KAAO,EAAG0B,KAAO,QAChC6D,KAAEA,KAAFiC,QAAQA,SAAatM,KAAKmM,WAAW,MAAO,UAC/CnG,OAASD,OAAO9B,YACTjE,KAAK0I,QAAQ,CAClB2B,KAAAA,KACAC,KAAMgC,QAAQ/F,MAAOzB,KAAM0B,gBAI9B5F,OAAOC,OAAOmF,OAAQ,CACrBW,IAAK3G,KACLuG,MAAAA,MACAzB,KAAAA,KACA0B,KAAAA,OAGMR;;;;;;;;;2BAYgBO,MAAOzB,KAAO,EAAG0B,KAAO,QAC3CR,aAAehG,KAAKgG,OAAOO,MAAOzB,KAAM0B,WAErCR,OAAOlB,MAAQkB,OAAO7B,aACtB6B,OACNA,aAAehG,KAAKgG,OAAOO,MAAOP,OAAOlB,KAAO,EAAG0B;;;;;;uBAUnC9D,UACb2H,KAAEA,KAAFiC,QAAQA,SAAatM,KAAKmM,WAAW,MAAO,sBAEzCpG,OAAO9B,YACPjE,KAAK0I,QAAQ,CAClB2B,KAAAA,KACAC,KAAMgC,QACL5J,gBAAgBC,KACbD,KAAKpC,IACJoC;;;;;;;;wBAcWrC,IAAKyE,KAAO,EAAG0B,KAAO,IAClCnG,eAAeD,MACpBC,IAAMD,IAAIU,IAAI,CAAER,IAAKD,WAClBgK,KAAEA,KAAFiC,QAAQA,SAAatM,KAAKmM,WAAW,MAAO,gBAC/CnG,OAASD,OAAO9B,YACTjE,KAAK0I,QAAQ,CAClB2B,KAAAA,KACAC,KAAMgC,QAAQjM,IAAIC,GAAIwE,KAAM0B,gBAI/B5F,OAAOC,OAAOmF,OAAQ,CACrBW,IAAO3G,KACPuG,MAAOlG,IACPyE,KAAAA,KACA0B,KAAAA,OAGMR;;;;;;mBASMuG,YACTlC,KAAEA,KAAFiC,QAAQA,SAAatM,KAAKmM,WAAW,MAAO,eAEzCxJ,KAAKsB,YACLjE,KAAK0I,QAAQ,CAClB2B,KAAAA,KACAC,KAAMgC,QAAQC;;;;;+BAWZlC,KAAEA,KAAFiC,QAAQA,SAAatM,KAAKmM,WAAW,MAAO,gCAGzCnM,KAAK0I,QAAQ,CAClB2B,KAAAA,KACAC,KAAMgC;CAEN,MAAOrF,YACFA,iBAAiBF,UACtB,MAAME,YACDoC,SAAWpC,MAAMC,iBAClBmC,UAAoC,MAAxBA,SAASC,WACzB,MAAMrC,YACD3G,KAAQ,MAAOkM,KAAKnD,SAASN,QAAQ0D,WAAa,IAAI,MACxDC,MAAMpM,IACT,MAAMyG,SAAS2C,OAAO,IAAI9C,MAAM,gBAAiByC,uBACrCrJ,KAAK2M,QAAQrM;;;;;;KAU5BsM,qBAAqBzK,aACd0K,kBAAoB1K,MAAMrC,KAAKkC;GAGX,SAAtB6K,kBAA8B;;OAMjB1K,MAAMO,KAAKa,MAIb,IACN,YAID;;;;;MAcPuJ,gBANgC,KACvB;;KACA;;IACA;;IACA,OAEkCD,0BAExCC,gBAEK,GAAEA,uBAIJD;;;;;;KAQRE,YAAY5K,UACPA,iBAAiBD,MAAO,KAI1BF,WAHGqI,KAAEA,KAAFiC,QAAQA,SAAanK,MAAMS,QAC3B5C,KAAKmM,WAAW,SAAU,aAC1BnM,KAAKmM,WAAW,SAAU;OAK7BnK,UADGG,MAAMS,QACG5C,KAAK4M,qBAAqBzK,OAG1BA,MAAMrC,KAAKkC,UAGhB,OAAMhC,KAAKyH,IAAM,IAAM,QAAQ4C,QAAUlI,MAAMS,QACpD0J,QAAQnK,MAAMO,KAAKa,MAAOvB,WAC1BsK,QAAQnK,MAAMO,KAAKa,MAAOpB,MAAM7B,GAAI0B,kBAElC,IAAI4E,MAAM;;;;;KAQjBoG,oBAAoB7K,UACfA,iBAAiBD,MAAO,KACvBmI,KAAEA,KAAFiC,QAAQA,SAAanK,MAAMS,QAC5B5C,KAAKmM,WAAW,SAAU,aAC1BnM,KAAKmM,WAAW,SAAU;MAGrB,OAAMnM,KAAKyH,IAAM,IAAM,QAAQ4C,QAAUlI,MAAMS,QACpD0J,QAAQnK,MAAMO,KAAKa,MAAOpB,MAAMrC,KAAKkC,WACrCsK,QAAQnK,MAAMO,KAAKa,MAAOpB,MAAM7B,GAAI6B,MAAMrC,KAAKkC,kBAE7C,IAAI4E,MAAM;;;;;KAQjBqG,oBAAoB9K,YACbA,iBAAiBD,OAAWC,MAAMS,eACjC,IAAIgE,MAAM,6CAGbyD,KAAEA,KAAFiC,QAAQA,SAAatM,KAAKmM,WAAW,SAAU,aAClDe,QAAW,OAAMlN,KAAKyH,IAAM,IAAM,QAAQ4C,OAC1C8C,YAAchL,MAAMrC,KAAKkC,UACzBoL,SAAW;;AAEXC,SAAWrN,KAAK4M,qBAAqBzK;OAEtCiL,SAASrI,KAAKmI,QAAUZ,QAAQnK,MAAMO,KAAKa,MAAO8J;AAGlDD,SAASrI,KAAKmI,QAAUZ,QAAQnK,MAAMO,KAAKa,MAAO4J;AAG9B,SAAhBA,cACHC,SAASrI,KAAKmI,QAAUZ,QAAQnK,MAAMO,KAAKa,MAAO,SAClD6J,SAASrI,KAAKmI,QAAUZ,QAAQnK,MAAMO,KAAKa,MAAO;AAI/B,SAAhB4J,aACHC,SAASrI,KAAKmI,QAAUZ,QAAQnK,MAAMO,KAAKa,MAAQ,GAAE4J,qBAI/C,IAAK,IAAIG,IAAIF;;;;;KAQrBG,YAAYpL,UACPA,iBAAiBD,QAAUC,MAAMS,QAAS,KACzCyH,KAAEA,KAAFiC,QAAQA,SAAatM,KAAKmM,WAAW,SAAU,mBAE3C,OAAMnM,KAAKyH,IAAM,IAAM,QAAQ4C,OACpCiC,QAAQnK,MAAMO,KAAKa,MAAOpB,MAAM7B,GAAI6B,MAAMrC,KAAKkC,iBAE7C,IAAI4E,MAAM,qEAhjBZC,cArFN,MAAMwF;;;;;;;;cAQS9F,MAAOzB,KAAO,EAAG0B,KAAO,UAC7B,+BAA8BD,cAAczB,OAAO0B,KAAO,SAAWA,KAAO;;;;;;yBASjEgH,MAAO1I,KAAO,SACzB,gCAA+B0I,cAAc1I;;;;;wBAQnCyH,cACV,gBAAeA;;;;;iBAQZA,cACH,gBAAeA;;;;;;sBASPkB,QAASzL,iBACjB,cAAayL,iBAAiBzL;;;;;;;qBAUvByL,QAAS3I,KAAM9C,iBACtB,cAAayL,WAAW3I,QAAQ9C;;;;;;;sBAUxByL,QAAS3I,KAAM9C,iBACvB,cAAayL,WAAW3I,SAAS9C;;;;uCAQlC;;;;;;;MEpGIuD,SAAW,CACvB5E,QAAWP,IAAIM,MAAMC,QACrBP,IAAWA,IAAIM,MAAMN,IACrBmB,SAAWnB,IAAIM,MAAMa,SACrBC,OAAWpB,IAAIM,MAAMc,OACrBC,OAAWrB,IAAIM,MAAMe,OACrBC,UAAWtB,IAAIM,MAAMgB,UACrBC,MAAWvB,IAAIM,MAAMiB,MACrBC,SAAWxB,IAAIM,MAAMkB"}